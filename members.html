<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Members - FitTrack Gym Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gym-primary': '#33AADA',
                        'gym-secondary': '#F59E0B',
                        'gym-accent': '#3B82F6',
                        'gym-blue': '#337ADE',
                        'gym-dark': '#111827',
                        'gym-darker': '#030712',
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* Dark theme colors (default) */
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --bg-tertiary: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-primary: #374151;
            --border-secondary: #4b5563;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --card-bg: #111827;
            --input-bg: #374151;
            --hover-bg: rgba(55, 65, 81, 0.5);

            /* Dark theme subtle gradient colors */
            --gradient-primary-dark: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.05) 0%, rgba(93, 66, 238, 0.05) 20%, rgba(66, 104, 228, 0.05) 40%, rgba(51, 122, 222, 0.05) 60%, rgba(51, 170, 218, 0.05) 80%, rgba(63, 224, 208, 0.05) 100%);
            --gradient-border-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-hover-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.08) 0%, rgba(93, 66, 238, 0.08) 20%, rgba(66, 104, 228, 0.08) 40%, rgba(51, 122, 222, 0.08) 60%, rgba(51, 170, 218, 0.08) 80%, rgba(63, 224, 208, 0.08) 100%);
            --gradient-accent-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        [data-theme="light"] {
            /* Light theme colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --card-bg: #ffffff;
            --input-bg: #f1f5f9;
            --hover-bg: rgba(241, 245, 249, 0.8);

            /* Vibrant gradient colors */
            --gradient-primary: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-border: linear-gradient(135deg, rgba(133, 84, 245, 0.3) 0%, rgba(93, 66, 238, 0.3) 20%, rgba(66, 104, 228, 0.3) 40%, rgba(51, 122, 222, 0.3) 60%, rgba(51, 170, 218, 0.3) 80%, rgba(63, 224, 208, 0.3) 100%);
            --gradient-hover: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        /* Theme transition */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
        }

        /* ===== NOTIFICATION-STYLE TOGGLE BUTTONS ===== */

        /* Base toggle button styles - compact notification button style */
        .header-toggle-btn {
            position: relative;
            padding: 6px;
            color: #9ca3af;
            transition: color 0.3s ease;
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            background: transparent;
        }

        .header-toggle-btn:hover {
            color: white;
        }

        /* Toggle icon styles - compact sizing */
        .header-toggle-icon {
            font-size: 14px;
            transition: all 0.3s ease;
        }

        /* Active state indicator - compact badge similar to notification badge */
        .header-toggle-btn.active::after {
            content: '';
            position: absolute;
            top: 4px;
            right: 4px;
            width: 6px;
            height: 6px;
            background: var(--gym-blue);
            border-radius: 50%;
            border: 1px solid var(--card-bg);
            animation: pulse-badge 2s infinite;
        }

        /* Active state color change */
        .header-toggle-btn.active {
            color: var(--gym-blue);
        }

        /* Subtle pulse animation for active badge */
        @keyframes pulse-badge {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.1);
            }
        }

        /* Focus states for accessibility */
        .header-toggle-btn:focus-visible {
            outline: 2px solid var(--gym-blue);
            outline-offset: 2px;
        }

        .header-toggle-btn:focus:not(:focus-visible) {
            outline: none;
        }

        .header-toggle-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            color: #6b7280;
        }

        .header-toggle-btn:disabled:hover {
            color: #6b7280;
        }

        .header-toggle-btn {
            z-index: 1001;
            position: relative;
        }

        /* Theme-specific enhancements */
        [data-theme="dark"] .header-toggle-btn {
            color: #9ca3af;
        }

        [data-theme="dark"] .header-toggle-btn:hover {
            color: white;
        }

        [data-theme="light"] .header-toggle-btn {
            color: #6b7280;
        }

        [data-theme="light"] .header-toggle-btn:hover {
            color: #374151;
        }

        [data-theme="light"] .header-toggle-btn.active {
            color: var(--gym-blue);
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans" style="background-color: var(--bg-primary); color: var(--text-primary);" data-theme="dark">
    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <!-- Main Layout Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col" style="background-color: var(--card-bg); border-right: 1px solid var(--border-primary);">
            <!-- Fixed Sidebar Header -->
            <div class="flex items-center justify-between h-12 px-4 flex-shrink-0" style="border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gym-primary rounded-md flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-xs"></i>
                    </div>
                    <span class="gradient-text text-lg font-bold text-white">FitTrack</span>
                </div>
                <button id="close-sidebar" class="lg:hidden text-gray-400 hover:text-white">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>

            <!-- Scrollable Navigation Container -->
            <div class="flex-1 overflow-y-auto sidebar-scroll">
                <nav class="mt-3 px-2 pb-3">
            <div class="space-y-0.5">
                <a href="index.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>
                    Dashboard
                </a>
                <a href="members.html" class="nav-link active flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-primary); background-color: var(--hover-bg);">
                    <i class="fas fa-users w-4 h-4 mr-2"></i>
                    Members
                </a>
                <a href="registration.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-user-plus w-4 h-4 mr-2"></i>
                    Registration
                </a>
                <a href="checkin.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-sign-in-alt w-4 h-4 mr-2"></i>
                    Check-in/Out
                </a>
                <a href="memberships.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-credit-card w-4 h-4 mr-2"></i>
                    Memberships
                </a>
                <a href="billing.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-receipt w-4 h-4 mr-2"></i>
                    Billing
                </a>
                <a href="schedule.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-calendar-alt w-4 h-4 mr-2"></i>
                    Class Schedule
                </a>
                <a href="statistics.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-chart-bar w-4 h-4 mr-2"></i>
                    Statistics
                </a>
            </div>

            <!-- Additional Menu Items -->
            <div class="mt-4 pt-3 border-t border-gray-800">
                <div class="space-y-0.5">
                    <a href="trainers.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-user-tie w-4 h-4 mr-2"></i>
                        Trainers
                    </a>
                    <a href="equipment.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-dumbbell w-4 h-4 mr-2"></i>
                        Equipment
                    </a>
                </div>
            </div>

            <div class="mt-4 pt-3 border-t border-gray-800">
                <div class="space-y-0.5">
                    <a href="settings.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-cog w-4 h-4 mr-2"></i>
                        Settings
                    </a>
                    <a href="profile.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-user-circle w-4 h-4 mr-2"></i>
                        Profile
                    </a>
                    <a href="#logout" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors text-red-400 hover:text-red-300 hover:bg-red-900/20">
                        <i class="fas fa-sign-out-alt w-4 h-4 mr-2"></i>
                        Logout
                    </a>
                </div>
                </div>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Enhanced Top Header -->
            <header class="px-2 py-2 sm:px-3 lg:px-4 flex-shrink-0 header-enhanced" style="background-color: var(--card-bg); border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center justify-between h-14">
                    <!-- Left Section: Logo, Menu, Title -->
                    <div class="flex items-center space-x-3">
                        <button id="open-sidebar" class="lg:hidden header-toggle-btn" aria-label="Open menu" title="Open navigation menu">
                            <i class="fas fa-bars header-toggle-icon"></i>
                        </button>

                        <!-- Page Title with Breadcrumb -->
                        <div class="flex items-center space-x-2">
                            <h1 class="gradient-text text-lg font-semibold text-white" id="page-title">Members</h1>
                            <div class="hidden md:flex items-center space-x-1 text-xs" style="color: var(--text-muted);">
                                <span>/</span>
                                <span id="page-subtitle">Management</span>
                            </div>
                        </div>
                    </div>

                    <!-- Center Section: Enhanced Search -->
                    <div class="hidden md:flex flex-1 max-w-md mx-4">
                        <div class="relative w-full">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-xs" style="color: var(--text-muted);"></i>
                            </div>
                            <input type="text"
                                   id="global-search"
                                   placeholder="Search members by name, email, or ID..."
                                   class="w-full pl-8 pr-10 py-1.5 text-sm rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gym-blue/50 focus:border-gym-blue"
                                   style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <kbd class="hidden lg:inline-flex items-center px-1.5 py-0.5 text-xs font-mono rounded border" style="background-color: var(--hover-bg); border-color: var(--border-primary); color: var(--text-muted);">⌘K</kbd>
                            </div>
                        </div>
                    </div>

                    <!-- Right Section: Action Buttons -->
                    <div class="flex items-center space-x-1 sm:space-x-2">
                        <!-- Mobile Search Toggle -->
                        <div class="md:hidden relative">
                            <button id="mobile-search-toggle" class="header-toggle-btn" aria-label="Search" title="Search">
                                <i class="fas fa-search header-toggle-icon"></i>
                            </button>
                        </div>

                        <!-- Quick Actions Dropdown -->
                        <div class="hidden sm:block relative">
                            <button id="quick-actions-btn" class="header-toggle-btn" aria-label="Quick actions" title="Quick actions">
                                <i class="fas fa-plus header-toggle-icon"></i>
                            </button>
                        </div>

                        <!-- Enhanced Notifications -->
                        <div class="relative">
                            <button id="notification-btn" class="header-toggle-btn" aria-label="Notifications" title="View notifications">
                                <i class="fas fa-bell header-toggle-icon"></i>
                                <span id="notification-badge" class="absolute -top-0.5 -right-0.5 bg-gym-secondary text-xs text-white rounded-full w-3.5 h-3.5 flex items-center justify-center text-[9px] notification-pulse">3</span>
                            </button>

                            <!-- Notification Dropdown -->
                            <div id="notification-dropdown" class="w-80 rounded-xl shadow-2xl hidden" style="background-color: var(--card-bg); border: 1px solid var(--border-primary); position: fixed; z-index: 9999;">
                                <!-- Dropdown Header -->
                                <div class="p-3 sm:p-4" style="border-bottom: 1px solid var(--border-primary);">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-base sm:text-lg font-semibold" style="color: var(--text-primary);">Notifications</h3>
                                        <button id="mark-all-read" class="text-gym-primary hover:text-gym-primary/80 text-xs sm:text-sm font-medium transition-colors">
                                            Mark all read
                                        </button>
                                    </div>
                                </div>

                                <!-- Notifications List -->
                                <div id="notifications-list" class="max-h-80 sm:max-h-96 overflow-y-auto">
                                    <!-- Notification items will be dynamically inserted here -->
                                </div>

                                <!-- Dropdown Footer -->
                                <div class="p-3 sm:p-4" style="border-top: 1px solid var(--border-primary);">
                                    <button id="view-all-notifications" class="w-full text-center text-gym-primary hover:text-gym-primary/80 text-xs sm:text-sm font-medium transition-colors">
                                        View all notifications
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced User Profile -->
                        <div class="hidden sm:flex relative">
                            <button id="profile-btn" class="flex items-center space-x-2 px-2 py-1 rounded-lg transition-all duration-200 hover:bg-white/5" aria-label="User profile" title="User profile and settings">
                                <div class="profile-avatar w-7 h-7 rounded-full flex items-center justify-center relative" style="background: linear-gradient(135deg, var(--gym-blue) 0%, var(--gym-secondary) 100%);">
                                    <i class="fas fa-user text-white text-xs"></i>
                                    <div class="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-white"></div>
                                </div>
                                <div class="hidden lg:block text-left">
                                    <div class="text-xs font-medium text-white">John Doe</div>
                                    <div class="text-xs" style="color: var(--text-muted);">Admin</div>
                                </div>
                                <i class="fas fa-chevron-down text-xs hidden lg:block" style="color: var(--text-muted);"></i>
                            </button>
                        </div>

                        <!-- Theme Toggle -->
                        <div class="relative">
                            <button id="theme-toggle" class="header-toggle-btn" aria-label="Toggle theme" title="Switch theme">
                                <i class="fas fa-moon header-toggle-icon" id="theme-icon"></i>
                            </button>
                        </div>

                        <!-- Fullscreen Toggle -->
                        <div class="relative">
                            <button id="fullscreen-toggle" class="header-toggle-btn" aria-label="Toggle fullscreen mode" title="Toggle fullscreen">
                                <i class="fas fa-expand header-toggle-icon" id="fullscreen-icon"></i>
                            </button>
                        </div>

                    </div>
                </div>
            </header>

            <!-- Mobile Search Overlay -->
            <div id="mobile-search-overlay" class="md:hidden fixed inset-x-0 top-14 z-50 hidden" style="background-color: var(--card-bg); border-bottom: 1px solid var(--border-primary);">
                <div class="p-3">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-sm" style="color: var(--text-muted);"></i>
                        </div>
                        <input type="text"
                               id="mobile-search"
                               placeholder="Search members by name, email, or ID..."
                               class="w-full pl-10 pr-10 py-2 text-sm rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gym-blue/50 focus:border-gym-blue"
                               style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                        <button id="mobile-search-close" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-times text-sm" style="color: var(--text-muted);"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main content area -->
            <main class="flex-1 overflow-y-auto p-2 sm:p-3 lg:p-4">
                <!-- Members Management Content -->
                <div class="space-y-4">
                    <!-- Page Header with Actions -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h2 class="text-xl font-semibold" style="color: var(--text-primary);">Member Management</h2>
                            <p class="text-sm mt-1" style="color: var(--text-muted);">Manage gym members, view profiles, and track memberships</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-4 py-2 bg-gym-blue text-white rounded-lg hover:bg-gym-blue/90 transition-colors text-sm font-medium">
                                <i class="fas fa-user-plus mr-2"></i>
                                Add Member
                            </button>
                            <button class="px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors text-sm font-medium">
                                <i class="fas fa-download mr-2"></i>
                                Export
                            </button>
                        </div>
                    </div>

                    <!-- Members Table -->
                    <div class="rounded-xl overflow-hidden" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="p-4" style="border-bottom: 1px solid var(--border-primary);">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Active Members</h3>
                                <div class="flex items-center space-x-2">
                                    <select class="px-3 py-1.5 text-sm rounded-lg border" style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                                        <option>All Members</option>
                                        <option>Active</option>
                                        <option>Inactive</option>
                                        <option>Expired</option>
                                    </select>
                                    <button class="px-3 py-1.5 text-sm border rounded-lg hover:bg-gray-700 transition-colors" style="border-color: var(--border-secondary); color: var(--text-secondary);">
                                        <i class="fas fa-filter mr-1"></i>
                                        Filter
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead style="background-color: var(--hover-bg);">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--text-muted);">Member</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--text-muted);">Membership</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--text-muted);">Status</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--text-muted);">Last Visit</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--text-muted);">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y" style="divide-color: var(--border-primary);">
                                    <tr class="hover:bg-gray-800/25 transition-colors">
                                        <td class="px-4 py-4">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 rounded-full bg-gym-primary flex items-center justify-center">
                                                    <span class="text-white font-medium text-sm">SJ</span>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium" style="color: var(--text-primary);">Sarah Johnson</div>
                                                    <div class="text-sm" style="color: var(--text-muted);"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="text-sm" style="color: var(--text-primary);">Premium</div>
                                            <div class="text-sm" style="color: var(--text-muted);">Expires: Dec 31, 2024</div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="text-sm" style="color: var(--text-primary);">Today, 2:30 PM</div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="flex items-center space-x-2">
                                                <button class="text-gym-blue hover:text-gym-blue/80 text-sm">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-white text-sm">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-400 hover:text-red-300 text-sm">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-800/25 transition-colors">
                                        <td class="px-4 py-4">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 rounded-full bg-gym-secondary flex items-center justify-center">
                                                    <span class="text-white font-medium text-sm">MR</span>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium" style="color: var(--text-primary);">Mike Rodriguez</div>
                                                    <div class="text-sm" style="color: var(--text-muted);"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="text-sm" style="color: var(--text-primary);">Basic</div>
                                            <div class="text-sm" style="color: var(--text-muted);">Expires: Nov 15, 2024</div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="text-sm" style="color: var(--text-primary);">Yesterday, 6:45 AM</div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="flex items-center space-x-2">
                                                <button class="text-gym-blue hover:text-gym-blue/80 text-sm">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-white text-sm">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-400 hover:text-red-300 text-sm">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-800/25 transition-colors">
                                        <td class="px-4 py-4">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 rounded-full bg-gym-accent flex items-center justify-center">
                                                    <span class="text-white font-medium text-sm">EC</span>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium" style="color: var(--text-primary);">Emily Chen</div>
                                                    <div class="text-sm" style="color: var(--text-muted);"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="text-sm" style="color: var(--text-primary);">Premium</div>
                                            <div class="text-sm" style="color: var(--text-muted);">Expires: Jan 20, 2025</div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Expiring Soon
                                            </span>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="text-sm" style="color: var(--text-primary);">3 days ago</div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <div class="flex items-center space-x-2">
                                                <button class="text-gym-blue hover:text-gym-blue/80 text-sm">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-white text-sm">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-400 hover:text-red-300 text-sm">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="px-4 py-3 flex items-center justify-between" style="border-top: 1px solid var(--border-primary);">
                            <div class="text-sm" style="color: var(--text-muted);">
                                Showing 1 to 3 of 247 members
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="px-3 py-1 text-sm border rounded hover:bg-gray-700 transition-colors" style="border-color: var(--border-secondary); color: var(--text-secondary);">
                                    Previous
                                </button>
                                <button class="px-3 py-1 text-sm bg-gym-blue text-white rounded">
                                    1
                                </button>
                                <button class="px-3 py-1 text-sm border rounded hover:bg-gray-700 transition-colors" style="border-color: var(--border-secondary); color: var(--text-secondary);">
                                    2
                                </button>
                                <button class="px-3 py-1 text-sm border rounded hover:bg-gray-700 transition-colors" style="border-color: var(--border-secondary); color: var(--text-secondary);">
                                    3
                                </button>
                                <button class="px-3 py-1 text-sm border rounded hover:bg-gray-700 transition-colors" style="border-color: var(--border-secondary); color: var(--text-secondary);">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Theme Manager
        class ThemeManager {
            constructor() {
                this.currentTheme = localStorage.getItem('gym-theme') || 'dark';
                this.setTheme(this.currentTheme);
                this.initializeToggle();
            }

            setTheme(theme) {
                this.currentTheme = theme;
                document.body.setAttribute('data-theme', theme);
                localStorage.setItem('gym-theme', theme);
                this.updateThemeToggleUI();
            }

            toggleTheme() {
                const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            }

            updateThemeToggleUI() {
                const toggleButton = document.getElementById('theme-toggle');
                const themeIcon = document.getElementById('theme-icon');

                if (toggleButton && themeIcon) {
                    if (this.currentTheme === 'light') {
                        toggleButton.classList.add('active');
                        toggleButton.title = 'Switch to dark theme';
                        toggleButton.setAttribute('aria-label', 'Switch to dark theme');
                        themeIcon.className = 'fas fa-sun header-toggle-icon';
                    } else {
                        toggleButton.classList.remove('active');
                        toggleButton.title = 'Switch to light theme';
                        toggleButton.setAttribute('aria-label', 'Switch to light theme');
                        themeIcon.className = 'fas fa-moon header-toggle-icon';
                    }
                }
            }

            initializeToggle() {
                const themeToggle = document.getElementById('theme-toggle');
                if (themeToggle) {
                    themeToggle.addEventListener('click', () => this.toggleTheme());
                }
            }
        }

        // Fullscreen Manager
        class FullscreenManager {
            constructor() {
                this.isFullscreen = false;
                this.initializeToggle();
                this.initializeEventListeners();
            }

            initializeToggle() {
                const fullscreenToggle = document.getElementById('fullscreen-toggle');
                if (fullscreenToggle) {
                    fullscreenToggle.addEventListener('click', () => this.toggleFullscreen());
                }
            }

            initializeEventListeners() {
                document.addEventListener('fullscreenchange', () => this.updateUI());
                document.addEventListener('webkitfullscreenchange', () => this.updateUI());
                document.addEventListener('mozfullscreenchange', () => this.updateUI());
                document.addEventListener('MSFullscreenChange', () => this.updateUI());
            }

            async toggleFullscreen() {
                try {
                    if (this.isCurrentlyFullscreen()) {
                        await this.exitFullscreen();
                    } else {
                        await this.enterFullscreen();
                    }
                } catch (error) {
                    console.error('Fullscreen toggle failed:', error);
                }
            }

            async enterFullscreen() {
                const element = document.documentElement;
                if (element.requestFullscreen) {
                    await element.requestFullscreen();
                } else if (element.webkitRequestFullscreen) {
                    await element.webkitRequestFullscreen();
                } else if (element.mozRequestFullScreen) {
                    await element.mozRequestFullScreen();
                } else if (element.msRequestFullscreen) {
                    await element.msRequestFullscreen();
                }
            }

            async exitFullscreen() {
                if (document.exitFullscreen) {
                    await document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    await document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    await document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    await document.msExitFullscreen();
                }
            }

            isCurrentlyFullscreen() {
                return !!(document.fullscreenElement ||
                         document.webkitFullscreenElement ||
                         document.mozFullScreenElement ||
                         document.msFullscreenElement);
            }

            updateUI() {
                const isFullscreen = this.isCurrentlyFullscreen();
                const button = document.getElementById('fullscreen-toggle');
                const fullscreenIcon = document.getElementById('fullscreen-icon');

                if (button && fullscreenIcon) {
                    if (isFullscreen) {
                        button.classList.add('active');
                        button.title = 'Exit fullscreen';
                        button.setAttribute('aria-label', 'Exit fullscreen mode');
                        fullscreenIcon.className = 'fas fa-compress header-toggle-icon';
                    } else {
                        button.classList.remove('active');
                        button.title = 'Enter fullscreen';
                        button.setAttribute('aria-label', 'Enter fullscreen mode');
                        fullscreenIcon.className = 'fas fa-expand header-toggle-icon';
                    }
                }

                this.isFullscreen = isFullscreen;
            }
        }

        // Enhanced Header Manager
        class HeaderManager {
            constructor() {
                this.initializeSearch();
                this.initializeMobileSearch();
                this.initializeKeyboardShortcuts();
            }

            initializeSearch() {
                const globalSearch = document.getElementById('global-search');
                const mobileSearch = document.getElementById('mobile-search');

                if (globalSearch) {
                    globalSearch.addEventListener('input', this.handleSearch.bind(this));
                }

                if (mobileSearch) {
                    mobileSearch.addEventListener('input', this.handleSearch.bind(this));
                }
            }

            handleSearch(event) {
                const query = event.target.value.trim();
                if (query.length > 2) {
                    console.log('Searching members for:', query);
                    // Implement member search logic here
                }
            }

            initializeMobileSearch() {
                const mobileSearchToggle = document.getElementById('mobile-search-toggle');
                const mobileSearchOverlay = document.getElementById('mobile-search-overlay');
                const mobileSearchClose = document.getElementById('mobile-search-close');
                const mobileSearchInput = document.getElementById('mobile-search');

                if (mobileSearchToggle && mobileSearchOverlay) {
                    mobileSearchToggle.addEventListener('click', () => {
                        mobileSearchOverlay.classList.toggle('hidden');
                        if (!mobileSearchOverlay.classList.contains('hidden')) {
                            setTimeout(() => mobileSearchInput?.focus(), 100);
                        }
                    });
                }

                if (mobileSearchClose) {
                    mobileSearchClose.addEventListener('click', () => {
                        mobileSearchOverlay.classList.add('hidden');
                    });
                }

                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && !mobileSearchOverlay?.classList.contains('hidden')) {
                        mobileSearchOverlay.classList.add('hidden');
                    }
                });
            }

            initializeKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                        e.preventDefault();
                        const searchInput = document.getElementById('global-search') || document.getElementById('mobile-search');
                        if (searchInput) {
                            if (window.innerWidth < 768) {
                                document.getElementById('mobile-search-toggle')?.click();
                            } else {
                                searchInput.focus();
                            }
                        }
                    }
                });
            }
        }

        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize managers
            window.themeManager = new ThemeManager();
            window.fullscreenManager = new FullscreenManager();
            window.headerManager = new HeaderManager();

            // Mobile menu toggle
            const openSidebar = document.getElementById('open-sidebar');
            const closeSidebar = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');

            function initializeSidebar() {
                if (window.innerWidth < 1024) {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');
                    overlay.classList.add('hidden');
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';
                } else {
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    overlay.classList.add('hidden');
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';
                }
            }

            function openSidebarMenu() {
                sidebar.classList.remove('-translate-x-full');
                sidebar.classList.add('translate-x-0');
                overlay.classList.remove('hidden');
                document.body.classList.add('sidebar-open');
                document.body.style.overflow = 'hidden';
            }

            function closeSidebarMenu() {
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
                overlay.classList.add('hidden');
                document.body.classList.remove('sidebar-open');
                document.body.style.overflow = '';
            }

            if (openSidebar) {
                openSidebar.addEventListener('click', openSidebarMenu);
            }

            if (closeSidebar) {
                closeSidebar.addEventListener('click', closeSidebarMenu);
            }

            if (overlay) {
                overlay.addEventListener('click', closeSidebarMenu);
            }

            window.addEventListener('resize', initializeSidebar);
            initializeSidebar();
        });
    </script>
</body>
</html>
