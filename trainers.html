<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trainers - FitTrack Gym Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gym-primary': '#33AADA',
                        'gym-secondary': '#F59E0B',
                        'gym-accent': '#3B82F6',
                        'gym-blue': '#337ADE',
                        'gym-dark': '#111827',
                        'gym-darker': '#030712',
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --bg-primary: #111827;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-primary: #374151;
            --card-bg: #111827;
            --hover-bg: rgba(55, 65, 81, 0.5);
        }
        [data-theme="light"] {
            --bg-primary: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-primary: #e2e8f0;
            --card-bg: #ffffff;
            --hover-bg: rgba(241, 245, 249, 0.8);
        }
        * { transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease; }
        .header-toggle-btn {
            position: relative; padding: 6px; color: #9ca3af; min-width: 44px; min-height: 44px;
            display: flex; align-items: center; justify-content: center; cursor: pointer; border: none; background: transparent;
        }
        .header-toggle-btn:hover { color: white; }
        .header-toggle-icon { font-size: 14px; }
        .header-toggle-btn.active { color: var(--gym-blue); }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans" style="background-color: var(--bg-primary); color: var(--text-primary);" data-theme="dark">
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>
    <div class="flex h-screen">
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col" style="background-color: var(--card-bg); border-right: 1px solid var(--border-primary);">
            <div class="flex items-center justify-between h-12 px-4 flex-shrink-0" style="border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gym-primary rounded-md flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-xs"></i>
                    </div>
                    <span class="gradient-text text-lg font-bold text-white">FitTrack</span>
                </div>
                <button id="close-sidebar" class="lg:hidden text-gray-400 hover:text-white">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto sidebar-scroll">
                <nav class="mt-3 px-2 pb-3">
                    <div class="space-y-0.5">
                        <a href="index.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>Dashboard
                        </a>
                        <a href="members.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-users w-4 h-4 mr-2"></i>Members
                        </a>
                        <a href="registration.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-user-plus w-4 h-4 mr-2"></i>Registration
                        </a>
                        <a href="checkin.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-sign-in-alt w-4 h-4 mr-2"></i>Check-in/Out
                        </a>
                        <a href="memberships.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-credit-card w-4 h-4 mr-2"></i>Memberships
                        </a>
                        <a href="billing.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-receipt w-4 h-4 mr-2"></i>Billing
                        </a>
                        <a href="schedule.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-calendar-alt w-4 h-4 mr-2"></i>Class Schedule
                        </a>
                        <a href="statistics.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                            <i class="fas fa-chart-bar w-4 h-4 mr-2"></i>Statistics
                        </a>
                    </div>
                    <div class="mt-4 pt-3 border-t border-gray-800">
                        <div class="space-y-0.5">
                            <a href="trainers.html" class="nav-link active flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-primary); background-color: var(--hover-bg);">
                                <i class="fas fa-user-tie w-4 h-4 mr-2"></i>Trainers
                            </a>
                            <a href="equipment.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                                <i class="fas fa-dumbbell w-4 h-4 mr-2"></i>Equipment
                            </a>
                        </div>
                    </div>
                    <div class="mt-4 pt-3 border-t border-gray-800">
                        <div class="space-y-0.5">
                            <a href="settings.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                                <i class="fas fa-cog w-4 h-4 mr-2"></i>Settings
                            </a>
                            <a href="profile.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                                <i class="fas fa-user-circle w-4 h-4 mr-2"></i>Profile
                            </a>
                            <a href="#logout" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors text-red-400 hover:text-red-300 hover:bg-red-900/20">
                                <i class="fas fa-sign-out-alt w-4 h-4 mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="px-2 py-2 sm:px-3 lg:px-4 flex-shrink-0 header-enhanced" style="background-color: var(--card-bg); border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center justify-between h-14">
                    <div class="flex items-center space-x-3">
                        <button id="open-sidebar" class="lg:hidden header-toggle-btn">
                            <i class="fas fa-bars header-toggle-icon"></i>
                        </button>
                        <div class="flex items-center space-x-2">
                            <h1 class="gradient-text text-lg font-semibold text-white">Trainers</h1>
                            <div class="hidden md:flex items-center space-x-1 text-xs" style="color: var(--text-muted);">
                                <span>/</span><span>Staff Management</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1 sm:space-x-2">
                        <div class="relative">
                            <button id="theme-toggle" class="header-toggle-btn">
                                <i class="fas fa-moon header-toggle-icon" id="theme-icon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>
            <main class="flex-1 overflow-y-auto p-2 sm:p-3 lg:p-4">
                <div class="max-w-6xl mx-auto">
                    <div class="text-center py-20">
                        <i class="fas fa-user-tie text-6xl mb-4" style="color: var(--gym-blue);"></i>
                        <h2 class="text-2xl font-bold mb-2" style="color: var(--text-primary);">Trainers Management</h2>
                        <p class="text-lg" style="color: var(--text-muted);">This page is under construction</p>
                        <p class="text-sm mt-2" style="color: var(--text-muted);">Trainer profiles and management will be available here</p>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <script>
        class ThemeManager {
            constructor() {
                this.currentTheme = localStorage.getItem('gym-theme') || 'dark';
                this.setTheme(this.currentTheme);
                this.initializeToggle();
            }
            setTheme(theme) {
                this.currentTheme = theme;
                document.body.setAttribute('data-theme', theme);
                localStorage.setItem('gym-theme', theme);
                this.updateThemeToggleUI();
            }
            toggleTheme() {
                const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            }
            updateThemeToggleUI() {
                const toggleButton = document.getElementById('theme-toggle');
                const themeIcon = document.getElementById('theme-icon');
                if (toggleButton && themeIcon) {
                    if (this.currentTheme === 'light') {
                        toggleButton.classList.add('active');
                        themeIcon.className = 'fas fa-sun header-toggle-icon';
                    } else {
                        toggleButton.classList.remove('active');
                        themeIcon.className = 'fas fa-moon header-toggle-icon';
                    }
                }
            }
            initializeToggle() {
                const themeToggle = document.getElementById('theme-toggle');
                if (themeToggle) {
                    themeToggle.addEventListener('click', () => this.toggleTheme());
                }
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            window.themeManager = new ThemeManager();
            const openSidebar = document.getElementById('open-sidebar');
            const closeSidebar = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');
            function initializeSidebar() {
                if (window.innerWidth < 1024) {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                } else {
                    sidebar.classList.remove('-translate-x-full');
                    overlay.classList.add('hidden');
                }
            }
            if (openSidebar) {
                openSidebar.addEventListener('click', () => {
                    sidebar.classList.remove('-translate-x-full');
                    overlay.classList.remove('hidden');
                });
            }
            if (closeSidebar) {
                closeSidebar.addEventListener('click', () => {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                });
            }
            if (overlay) {
                overlay.addEventListener('click', () => {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                });
            }
            window.addEventListener('resize', initializeSidebar);
            initializeSidebar();
        });
    </script>
</body>
</html>
