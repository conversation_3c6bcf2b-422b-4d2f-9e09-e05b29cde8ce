<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitTrack - Gym Member Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gym-primary': '#33AADA',
                        'gym-secondary': '#F59E0B',
                        'gym-accent': '#3B82F6',
                        'gym-blue': '#337ADE',
                        'gym-dark': '#111827',
                        'gym-darker': '#030712',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-down': 'slideDown 0.2s ease-out',
                        'pulse-badge': 'pulseBadge 2s infinite',
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* CSS Custom Properties for theming */
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-primary: #374151;
            --card-bg: #111827;
            --input-bg: #374151;
            --hover-bg: rgba(55, 65, 81, 0.5);
            --gym-primary-rgb: 51, 170, 218;
            --gym-secondary-rgb: 245, 158, 11;
        }

        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-primary: #e2e8f0;
            --card-bg: #ffffff;
            --input-bg: #f1f5f9;
            --hover-bg: rgba(241, 245, 249, 0.8);
        }

        /* Base styles with CSS custom properties */
        * {
            transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
        }

        /* Optimized notification styles */
        .header-toggle-btn {
            position: relative;
            padding: 6px;
            color: var(--text-muted);
            transition: color 0.2s ease;
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            background: transparent;
        }

        .header-toggle-btn:hover {
            color: var(--text-primary);
        }

        .header-toggle-btn.active::after {
            content: '';
            position: absolute;
            top: 4px;
            right: 4px;
            width: 6px;
            height: 6px;
            background: var(--gym-primary-rgb);
            border-radius: 50%;
            animation: pulse-badge 2s infinite;
        }

        /* Optimized card styles */
        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-primary);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Navigation styles */
        .nav-link {
            color: var(--text-secondary);
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            color: var(--text-primary);
            background-color: var(--hover-bg);
        }

        .nav-link.active {
            color: var(--text-primary);
            background-color: var(--hover-bg);
            border-left: 3px solid rgb(var(--gym-primary-rgb));
        }

        /* Form styles */
        .form-input {
            width: 100%;
            padding: 0.75rem;
            background-color: var(--input-bg);
            border: 1px solid var(--border-primary);
            border-radius: 0.5rem;
            color: var(--text-primary);
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: rgb(var(--gym-primary-rgb));
            box-shadow: 0 0 0 3px rgba(var(--gym-primary-rgb), 0.1);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        /* Button styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .btn-primary {
            background-color: rgb(var(--gym-primary-rgb));
            color: white;
        }

        .btn-primary:hover {
            background-color: rgba(var(--gym-primary-rgb), 0.8);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--hover-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background-color: var(--border-primary);
        }

        /* Stats card optimization */
        .stats-card {
            background: var(--card-bg);
            border: 1px solid var(--border-primary);
            border-radius: 0.75rem;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, 
                rgb(var(--gym-primary-rgb)), 
                rgb(var(--gym-secondary-rgb))
            );
        }

        /* Optimized dropdown styles */
        .dropdown {
            position: fixed;
            background: var(--card-bg);
            border: 1px solid var(--border-primary);
            border-radius: 0.75rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            animation: slide-down 0.2s ease-out;
        }

        /* Responsive optimizations */
        @media (max-width: 768px) {
            .card {
                padding: 0.75rem;
            }
            
            .stats-card {
                padding: 1rem;
            }
            
            .btn {
                padding: 0.625rem 1rem;
                font-size: 0.875rem;
            }
            
            .header-toggle-btn {
                min-width: 40px;
                min-height: 40px;
                padding: 4px;
            }
        }

        @media (max-width: 640px) {
            .card {
                padding: 0.5rem;
            }
            
            .stats-card {
                padding: 0.75rem;
            }
            
            .form-input {
                padding: 0.625rem;
                font-size: 0.875rem;
            }
        }

        /* Animation keyframes */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulseBadge {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.1); }
        }

        /* Scrollbar optimization */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: rgb(var(--gym-primary-rgb));
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(var(--gym-primary-rgb), 0.8);
        }

        /* Utility classes */
        .hidden { display: none; }
        .flex { display: flex; }
        .grid { display: grid; }
        .text-center { text-align: center; }
        .font-bold { font-weight: 700; }
        .font-medium { font-weight: 500; }
        .text-sm { font-size: 0.875rem; }
        .text-xs { font-size: 0.75rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .rounded-xl { border-radius: 0.75rem; }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .transition-all { transition: all 0.2s ease; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body data-theme="dark">
    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <!-- Main Layout Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <aside id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col" style="background-color: var(--card-bg); border-right: 1px solid var(--border-primary);">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-4 border-b" style="border-color: var(--border-primary);">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-sm"></i>
                    </div>
                    <span class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500">FitTrack</span>
                </div>
                <button id="close-sidebar" class="lg:hidden text-gray-400 hover:text-white p-2 rounded-lg">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 overflow-y-auto px-4 py-6 space-y-2">
                <a href="#dashboard" class="nav-link active">
                    <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                    Dashboard
                </a>
                <a href="#members" class="nav-link">
                    <i class="fas fa-users w-5 h-5 mr-3"></i>
                    Members
                </a>
                <a href="#registration" class="nav-link">
                    <i class="fas fa-user-plus w-5 h-5 mr-3"></i>
                    Registration
                </a>
                <a href="#checkin" class="nav-link">
                    <i class="fas fa-sign-in-alt w-5 h-5 mr-3"></i>
                    Check-in/Out
                </a>
                <a href="#memberships" class="nav-link">
                    <i class="fas fa-credit-card w-5 h-5 mr-3"></i>
                    Memberships
                </a>
                <a href="#billing" class="nav-link">
                    <i class="fas fa-receipt w-5 h-5 mr-3"></i>
                    Billing
                </a>
                <a href="#schedule" class="nav-link">
                    <i class="fas fa-calendar-alt w-5 h-5 mr-3"></i>
                    Class Schedule
                </a>
                <a href="#statistics" class="nav-link">
                    <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                    Statistics
                </a>

                <div class="border-t pt-4 mt-4" style="border-color: var(--border-primary);">
                    <a href="#settings" class="nav-link">
                        <i class="fas fa-cog w-5 h-5 mr-3"></i>
                        Settings
                    </a>
                    <a href="#logout" class="nav-link text-red-400 hover:text-red-300">
                        <i class="fas fa-sign-out-alt w-5 h-5 mr-3"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="flex items-center justify-between h-16 px-6 border-b" style="background-color: var(--card-bg); border-color: var(--border-primary);">
                <!-- Left Section -->
                <div class="flex items-center space-x-4">
                    <button id="open-sidebar" class="lg:hidden header-toggle-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-semibold" style="color: var(--text-primary);" id="page-title">Dashboard</h1>
                        <p class="text-sm" style="color: var(--text-muted);" id="page-subtitle">Overview</p>
                    </div>
                </div>

                <!-- Right Section -->
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <div class="hidden md:block relative">
                        <input type="text" placeholder="Search..." class="form-input w-64 pl-10">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2" style="color: var(--text-muted);"></i>
                    </div>

                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notification-btn" class="header-toggle-btn">
                            <i class="fas fa-bell"></i>
                            <span id="notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                        </button>
                        
                        <!-- Notification Dropdown -->
                        <div id="notification-dropdown" class="dropdown hidden w-80">
                            <div class="p-4 border-b" style="border-color: var(--border-primary);">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold" style="color: var(--text-primary);">Notifications</h3>
                                    <button class="text-sm font-medium text-blue-500 hover:text-blue-400">Mark all read</button>
                                </div>
                            </div>
                            <div class="max-h-96 overflow-y-auto p-2">
                                <div class="p-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg cursor-pointer">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user-plus text-blue-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium text-sm" style="color: var(--text-primary);">New member registration</p>
                                            <p class="text-sm" style="color: var(--text-secondary);">Sarah Johnson joined as Premium member</p>
                                            <p class="text-xs" style="color: var(--text-muted);">2 minutes ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="header-toggle-btn">
                        <i class="fas fa-moon" id="theme-icon"></i>
                    </button>

                    <!-- Profile -->
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <span class="hidden md:block font-medium" style="color: var(--text-primary);">Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="space-y-6">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="stats-card">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium" style="color: var(--text-muted);">Total Members</p>
                                    <p class="text-3xl font-bold" style="color: var(--text-primary);">1,247</p>
                                    <p class="text-green-500 text-sm mt-1">
                                        <i class="fas fa-arrow-up mr-1"></i>+12% from last month
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-users text-blue-600 text-xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium" style="color: var(--text-muted);">Active Today</p>
                                    <p class="text-3xl font-bold" style="color: var(--text-primary);">89</p>
                                    <p class="text-green-500 text-sm mt-1">
                                        <i class="fas fa-arrow-up mr-1"></i>+5% from yesterday
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-sign-in-alt text-green-600 text-xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium" style="color: var(--text-muted);">Revenue</p>
                                    <p class="text-3xl font-bold" style="color: var(--text-primary);">$24,580</p>
                                    <p class="text-green-500 text-sm mt-1">
                                        <i class="fas fa-arrow-up mr-1"></i>+8% from last month
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium" style="color: var(--text-muted);">Classes Today</p>
                                    <p class="text-3xl font-bold" style="color: var(--text-primary);">12</p>
                                    <p class="text-blue-500 text-sm mt-1">
                                        <i class="fas fa-check mr-1"></i>All scheduled
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-calendar-check text-purple-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions & Recent Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Quick Actions -->
                        <div class="card">
                            <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Quick Actions</h3>
                            <div class="space-y-3">
                                <button class="btn btn-primary w-full text-left">
                                    <i class="fas fa-user-plus mr-3"></i>
                                    Add New Member
                                </button>
                                <button class="btn btn-secondary w-full text-left">
                                    <i class="fas fa-sign-in-alt mr-3"></i>
                                    Quick Check-in
                                </button>
                                <button class="btn btn-secondary w-full text-left">
                                    <i class="fas fa-calendar-plus mr-3"></i>
                                    Schedule Class
                                </button>
                                <button class="btn btn-secondary w-full text-left">
                                    <i class="fas fa-chart-line mr-3"></i>
                                    View Reports
                                </button>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="card lg:col-span-2">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Recent Activity</h3>
                                <button class="text-blue-500 hover:text-blue-400 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center space-x-4 p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-sign-in-alt text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium" style="color: var(--text-primary);">John Smith checked in</p>
                                        <p class="text-sm" style="color: var(--text-muted);">Premium member • 2 minutes ago</p>
                                    </div>
                                    <span class="text-green-500 text-sm font-medium">Active</span>
                                </div>

                                <div class="flex items-center space-x-4 p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-plus text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium" style="color: var(--text-primary);">Sarah Johnson registered</p>
                                        <p class="text-sm" style="color: var(--text-muted);">Basic membership • 15 minutes ago</p>
                                    </div>
                                    <span class="text-blue-500 text-sm font-medium">New</span>
                                </div>

                                <div class="flex items-center space-x-4 p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-credit-card text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium" style="color: var(--text-primary);">Payment received from Mike Davis</p>
                                        <p class="text-sm" style="color: var(--text-muted);">$89.99 • 1 hour ago</p>
                                    </div>
                                    <span class="text-green-500 text-sm font-medium">Paid</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Optimized JavaScript -->
    <script>
        // Optimized Application Class
        class GymApp {
            constructor() {
                this.currentTheme = localStorage.getItem('gym-theme') || 'dark';
                this.currentSection = 'dashboard';
                this.notifications = [];
                this.init();
            }

            init() {
                this.setupTheme();
                this.setupNavigation();
                this.setupMobileMenu();
                this.setupNotifications();
                this.setupEventListeners();
                this.loadInitialData();
            }

            // Theme management
            setupTheme() {
                document.body.setAttribute('data-theme', this.currentTheme);
                this.updateThemeIcon();
                
                document.getElementById('theme-toggle').addEventListener('click', () => {
                    this.toggleTheme();
                });
            }

            toggleTheme() {
                this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                document.body.setAttribute('data-theme', this.currentTheme);
                localStorage.setItem('gym-theme', this.currentTheme);
                this.updateThemeIcon();
            }

            updateThemeIcon() {
                const icon = document.getElementById('theme-icon');
                icon.className = this.currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            // Navigation
            setupNavigation() {
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const section = link.getAttribute('href').substring(1);
                        this.navigateToSection(section);
                    });
                });
            }

            navigateToSection(section) {
                // Update active nav link
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                document.querySelector(`[href="#${section}"]`).classList.add('active');

                // Update page title
                const titles = {
                    dashboard: 'Dashboard',
                    members: 'Members',
                    registration: 'Registration',
                    checkin: 'Check-in/Out',
                    memberships: 'Memberships',
                    billing: 'Billing',
                    schedule: 'Class Schedule',
                    statistics: 'Statistics'
                };

                document.getElementById('page-title').textContent = titles[section] || 'Dashboard';
                document.getElementById('page-subtitle').textContent = 'Overview';

                this.currentSection = section;
                this.closeMobileMenu();
            }

            // Mobile menu
            setupMobileMenu() {
                const openBtn = document.getElementById('open-sidebar');
                const closeBtn = document.getElementById('close-sidebar');
                const overlay = document.getElementById('mobile-menu-overlay');
                const sidebar = document.getElementById('sidebar');

                openBtn.addEventListener('click', () => this.openMobileMenu());
                closeBtn.addEventListener('click', () => this.closeMobileMenu());
                overlay.addEventListener('click', () => this.closeMobileMenu());
            }

            openMobileMenu() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('mobile-menu-overlay');
                
                sidebar.classList.remove('-translate-x-full');
                sidebar.classList.add('translate-x-0');
                overlay.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            closeMobileMenu() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('mobile-menu-overlay');
                
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
                overlay.classList.add('hidden');
                document.body.style.overflow = '';
            }

            // Notifications
            setupNotifications() {
                const notificationBtn = document.getElementById('notification-btn');
                const dropdown = document.getElementById('notification-dropdown');

                notificationBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleNotificationDropdown();
                });

                document.addEventListener('click', (e) => {
                    if (!dropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
                        dropdown.classList.add('hidden');
                    }
                });
            }

            toggleNotificationDropdown() {
                const dropdown = document.getElementById('notification-dropdown');
                const btn = document.getElementById('notification-btn');
                
                if (dropdown.classList.contains('hidden')) {
                    this.positionDropdown(dropdown, btn);
                    dropdown.classList.remove('hidden');
                } else {
                    dropdown.classList.add('hidden');
                }
            }

            positionDropdown(dropdown, trigger) {
                const rect = trigger.getBoundingClientRect();
                dropdown.style.top = `${rect.bottom + 8}px`;
                dropdown.style.right = `${window.innerWidth - rect.right}px`;
            }

            // Event listeners
            setupEventListeners() {
                // Quick action buttons
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.addEventListener('click', this.handleQuickAction.bind(this));
                });

                // Search input
                const searchInput = document.querySelector('input[placeholder="Search..."]');
                if (searchInput) {
                    searchInput.addEventListener('input', this.handleSearch.bind(this));
                }

                // Responsive handling
                window.addEventListener('resize', this.handleResize.bind(this));
            }

            handleQuickAction(e) {
                const action = e.target.textContent.trim();
                console.log('Quick action:', action);
                
                // Add action-specific logic here
                switch(action) {
                    case 'Add New Member':
                        this.navigateToSection('registration');
                        break;
                    case 'Quick Check-in':
                        this.navigateToSection('checkin');
                        break;
                    case 'Schedule Class':
                        this.navigateToSection('schedule');
                        break;
                    case 'View Reports':
                        this.navigateToSection('statistics');
                        break;
                }
            }

            handleSearch(e) {
                const query = e.target.value.trim();
                if (query.length > 2) {
                    console.log('Searching for:', query);
                    // Implement search logic
                }
            }

            handleResize() {
                if (window.innerWidth >= 1024) {
                    this.closeMobileMenu();
                }
            }

            // Data loading
            loadInitialData() {
                // Simulate loading dashboard data
                this.updateStats();
                this.loadNotifications();
            }

            updateStats() {
                // Update dashboard statistics
                // This could be connected to an API
                console.log('Stats updated');
            }

            loadNotifications() {
                this.notifications = [
                    {
                        id: 1,
                        title: 'New member registration',
                        message: 'Sarah Johnson joined as Premium member',
                        time: '2 minutes ago',
                        type: 'success'
                    }
                ];
                this.updateNotificationBadge();
            }

            updateNotificationBadge() {
                const badge = document.getElementById('notification-badge');
                const unreadCount = this.notifications.filter(n => !n.read).length;
                
                if (unreadCount > 0) {
                    badge.textContent = unreadCount;
                    badge.classList.remove('hidden');
                } else {
                    badge.classList.add('hidden');
                }
            }
        }

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.gymApp = new GymApp();
        });
    </script>
</body>
</html>