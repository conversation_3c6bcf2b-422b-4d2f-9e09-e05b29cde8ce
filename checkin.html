<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check-in/Out - FitTrack Gym Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gym-primary': '#33AADA',
                        'gym-secondary': '#F59E0B',
                        'gym-accent': '#3B82F6',
                        'gym-blue': '#337ADE',
                        'gym-dark': '#111827',
                        'gym-darker': '#030712',
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* Dark theme colors (default) */
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --bg-tertiary: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-primary: #374151;
            --border-secondary: #4b5563;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --card-bg: #111827;
            --input-bg: #374151;
            --hover-bg: rgba(55, 65, 81, 0.5);

            /* Dark theme subtle gradient colors */
            --gradient-primary-dark: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.05) 0%, rgba(93, 66, 238, 0.05) 20%, rgba(66, 104, 228, 0.05) 40%, rgba(51, 122, 222, 0.05) 60%, rgba(51, 170, 218, 0.05) 80%, rgba(63, 224, 208, 0.05) 100%);
            --gradient-border-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-hover-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.08) 0%, rgba(93, 66, 238, 0.08) 20%, rgba(66, 104, 228, 0.08) 40%, rgba(51, 122, 222, 0.08) 60%, rgba(51, 170, 218, 0.08) 80%, rgba(63, 224, 208, 0.08) 100%);
            --gradient-accent-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        [data-theme="light"] {
            /* Light theme colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --card-bg: #ffffff;
            --input-bg: #f1f5f9;
            --hover-bg: rgba(241, 245, 249, 0.8);

            /* Vibrant gradient colors */
            --gradient-primary: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-border: linear-gradient(135deg, rgba(133, 84, 245, 0.3) 0%, rgba(93, 66, 238, 0.3) 20%, rgba(66, 104, 228, 0.3) 40%, rgba(51, 122, 222, 0.3) 60%, rgba(51, 170, 218, 0.3) 80%, rgba(63, 224, 208, 0.3) 100%);
            --gradient-hover: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        /* Theme transition */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
        }

        /* ===== NOTIFICATION-STYLE TOGGLE BUTTONS ===== */

        /* Base toggle button styles - compact notification button style */
        .header-toggle-btn {
            position: relative;
            padding: 6px;
            color: #9ca3af;
            transition: color 0.3s ease;
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            background: transparent;
        }

        .header-toggle-btn:hover {
            color: white;
        }

        /* Toggle icon styles - compact sizing */
        .header-toggle-icon {
            font-size: 14px;
            transition: all 0.3s ease;
        }

        /* Active state indicator - compact badge similar to notification badge */
        .header-toggle-btn.active::after {
            content: '';
            position: absolute;
            top: 4px;
            right: 4px;
            width: 6px;
            height: 6px;
            background: var(--gym-blue);
            border-radius: 50%;
            border: 1px solid var(--card-bg);
            animation: pulse-badge 2s infinite;
        }

        /* Active state color change */
        .header-toggle-btn.active {
            color: var(--gym-blue);
        }

        /* Subtle pulse animation for active badge */
        @keyframes pulse-badge {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.1);
            }
        }

        /* Focus states for accessibility */
        .header-toggle-btn:focus-visible {
            outline: 2px solid var(--gym-blue);
            outline-offset: 2px;
        }

        .header-toggle-btn:focus:not(:focus-visible) {
            outline: none;
        }

        .header-toggle-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            color: #6b7280;
        }

        .header-toggle-btn:disabled:hover {
            color: #6b7280;
        }

        .header-toggle-btn {
            z-index: 1001;
            position: relative;
        }

        /* Theme-specific enhancements */
        [data-theme="dark"] .header-toggle-btn {
            color: #9ca3af;
        }

        [data-theme="dark"] .header-toggle-btn:hover {
            color: white;
        }

        [data-theme="light"] .header-toggle-btn {
            color: #6b7280;
        }

        [data-theme="light"] .header-toggle-btn:hover {
            color: #374151;
        }

        [data-theme="light"] .header-toggle-btn.active {
            color: var(--gym-blue);
        }

        /* Check-in specific styles */
        .checkin-card {
            background: var(--card-bg);
            border: 1px solid var(--border-primary);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .checkin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-checked-in {
            background-color: rgba(34, 197, 94, 0.1);
            color: #22c55e;
        }

        .status-checked-out {
            background-color: rgba(156, 163, 175, 0.1);
            color: #9ca3af;
        }

        .btn-checkin {
            background-color: #22c55e;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-checkin:hover {
            background-color: #16a34a;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
        }

        .btn-checkout {
            background-color: #ef4444;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-checkout:hover {
            background-color: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .search-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--border-secondary);
            border-radius: 0.75rem;
            background-color: var(--input-bg);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--gym-blue);
            box-shadow: 0 0 0 4px rgba(51, 122, 222, 0.1);
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans" style="background-color: var(--bg-primary); color: var(--text-primary);" data-theme="dark">
    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <!-- Main Layout Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col" style="background-color: var(--card-bg); border-right: 1px solid var(--border-primary);">
            <!-- Fixed Sidebar Header -->
            <div class="flex items-center justify-between h-12 px-4 flex-shrink-0" style="border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gym-primary rounded-md flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-xs"></i>
                    </div>
                    <span class="gradient-text text-lg font-bold text-white">FitTrack</span>
                </div>
                <button id="close-sidebar" class="lg:hidden text-gray-400 hover:text-white">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>

            <!-- Scrollable Navigation Container -->
            <div class="flex-1 overflow-y-auto sidebar-scroll">
                <nav class="mt-3 px-2 pb-3">
            <div class="space-y-0.5">
                <a href="index.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>
                    Dashboard
                </a>
                <a href="members.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-users w-4 h-4 mr-2"></i>
                    Members
                </a>
                <a href="registration.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-user-plus w-4 h-4 mr-2"></i>
                    Registration
                </a>
                <a href="checkin.html" class="nav-link active flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-primary); background-color: var(--hover-bg);">
                    <i class="fas fa-sign-in-alt w-4 h-4 mr-2"></i>
                    Check-in/Out
                </a>
                <a href="memberships.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-credit-card w-4 h-4 mr-2"></i>
                    Memberships
                </a>
                <a href="billing.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-receipt w-4 h-4 mr-2"></i>
                    Billing
                </a>
                <a href="schedule.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-calendar-alt w-4 h-4 mr-2"></i>
                    Class Schedule
                </a>
                <a href="statistics.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-chart-bar w-4 h-4 mr-2"></i>
                    Statistics
                </a>
            </div>

            <!-- Additional Menu Items -->
            <div class="mt-4 pt-3 border-t border-gray-800">
                <div class="space-y-0.5">
                    <a href="trainers.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-user-tie w-4 h-4 mr-2"></i>
                        Trainers
                    </a>
                    <a href="equipment.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-dumbbell w-4 h-4 mr-2"></i>
                        Equipment
                    </a>
                </div>
            </div>

            <div class="mt-4 pt-3 border-t border-gray-800">
                <div class="space-y-0.5">
                    <a href="settings.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-cog w-4 h-4 mr-2"></i>
                        Settings
                    </a>
                    <a href="profile.html" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                        <i class="fas fa-user-circle w-4 h-4 mr-2"></i>
                        Profile
                    </a>
                    <a href="#logout" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors text-red-400 hover:text-red-300 hover:bg-red-900/20">
                        <i class="fas fa-sign-out-alt w-4 h-4 mr-2"></i>
                        Logout
                    </a>
                </div>
                </div>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Enhanced Top Header -->
            <header class="px-2 py-2 sm:px-3 lg:px-4 flex-shrink-0 header-enhanced" style="background-color: var(--card-bg); border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center justify-between h-14">
                    <!-- Left Section: Logo, Menu, Title -->
                    <div class="flex items-center space-x-3">
                        <button id="open-sidebar" class="lg:hidden header-toggle-btn" aria-label="Open menu" title="Open navigation menu">
                            <i class="fas fa-bars header-toggle-icon"></i>
                        </button>

                        <!-- Page Title with Breadcrumb -->
                        <div class="flex items-center space-x-2">
                            <h1 class="gradient-text text-lg font-semibold text-white" id="page-title">Check-in/Out</h1>
                            <div class="hidden md:flex items-center space-x-1 text-xs" style="color: var(--text-muted);">
                                <span>/</span>
                                <span id="page-subtitle">Member Access</span>
                            </div>
                        </div>
                    </div>

                    <!-- Center Section: Enhanced Search -->
                    <div class="hidden md:flex flex-1 max-w-md mx-4">
                        <div class="relative w-full">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-xs" style="color: var(--text-muted);"></i>
                            </div>
                            <input type="text"
                                   id="global-search"
                                   placeholder="Search members, classes, or equipment..."
                                   class="w-full pl-8 pr-10 py-1.5 text-sm rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gym-blue/50 focus:border-gym-blue"
                                   style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <kbd class="hidden lg:inline-flex items-center px-1.5 py-0.5 text-xs font-mono rounded border" style="background-color: var(--hover-bg); border-color: var(--border-primary); color: var(--text-muted);">⌘K</kbd>
                            </div>
                        </div>
                    </div>

                    <!-- Right Section: Action Buttons -->
                    <div class="flex items-center space-x-1 sm:space-x-2">
                        <!-- Mobile Search Toggle -->
                        <div class="md:hidden relative">
                            <button id="mobile-search-toggle" class="header-toggle-btn" aria-label="Search" title="Search">
                                <i class="fas fa-search header-toggle-icon"></i>
                            </button>
                        </div>

                        <!-- Quick Actions Dropdown -->
                        <div class="hidden sm:block relative">
                            <button id="quick-actions-btn" class="header-toggle-btn" aria-label="Quick actions" title="Quick actions">
                                <i class="fas fa-plus header-toggle-icon"></i>
                            </button>
                        </div>

                        <!-- Enhanced Notifications -->
                        <div class="relative">
                            <button id="notification-btn" class="header-toggle-btn" aria-label="Notifications" title="View notifications">
                                <i class="fas fa-bell header-toggle-icon"></i>
                                <span id="notification-badge" class="absolute -top-0.5 -right-0.5 bg-gym-secondary text-xs text-white rounded-full w-3.5 h-3.5 flex items-center justify-center text-[9px] notification-pulse">3</span>
                            </button>
                        </div>

                        <!-- Enhanced User Profile -->
                        <div class="hidden sm:flex relative">
                            <button id="profile-btn" class="flex items-center space-x-2 px-2 py-1 rounded-lg transition-all duration-200 hover:bg-white/5" aria-label="User profile" title="User profile and settings">
                                <div class="profile-avatar w-7 h-7 rounded-full flex items-center justify-center relative" style="background: linear-gradient(135deg, var(--gym-blue) 0%, var(--gym-secondary) 100%);">
                                    <i class="fas fa-user text-white text-xs"></i>
                                    <div class="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-white"></div>
                                </div>
                                <div class="hidden lg:block text-left">
                                    <div class="text-xs font-medium text-white">John Doe</div>
                                    <div class="text-xs" style="color: var(--text-muted);">Admin</div>
                                </div>
                                <i class="fas fa-chevron-down text-xs hidden lg:block" style="color: var(--text-muted);"></i>
                            </button>
                        </div>

                        <!-- Theme Toggle -->
                        <div class="relative">
                            <button id="theme-toggle" class="header-toggle-btn" aria-label="Toggle theme" title="Switch theme">
                                <i class="fas fa-moon header-toggle-icon" id="theme-icon"></i>
                            </button>
                        </div>

                        <!-- Fullscreen Toggle -->
                        <div class="relative">
                            <button id="fullscreen-toggle" class="header-toggle-btn" aria-label="Toggle fullscreen mode" title="Toggle fullscreen">
                                <i class="fas fa-expand header-toggle-icon" id="fullscreen-icon"></i>
                            </button>
                        </div>

                    </div>
                </div>
            </header>

            <!-- Mobile Search Overlay -->
            <div id="mobile-search-overlay" class="md:hidden fixed inset-x-0 top-14 z-50 hidden" style="background-color: var(--card-bg); border-bottom: 1px solid var(--border-primary);">
                <div class="p-3">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-sm" style="color: var(--text-muted);"></i>
                        </div>
                        <input type="text"
                               id="mobile-search"
                               placeholder="Search members, classes, or equipment..."
                               class="w-full pl-10 pr-10 py-2 text-sm rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gym-blue/50 focus:border-gym-blue"
                               style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                        <button id="mobile-search-close" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-times text-sm" style="color: var(--text-muted);"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main content area -->
            <main class="flex-1 overflow-y-auto p-2 sm:p-3 lg:p-4">
                <!-- Check-in/Out Content -->
                <div class="max-w-6xl mx-auto space-y-6">
                    <!-- Page Header -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h2 class="text-xl font-semibold" style="color: var(--text-primary);">Member Check-in/Out</h2>
                            <p class="text-sm mt-1" style="color: var(--text-muted);">Track member visits and manage gym access</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="text-sm" style="color: var(--text-muted);">
                                <span class="font-medium">Currently in gym:</span>
                                <span class="text-gym-primary font-bold">47 members</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Check-in Search -->
                    <div class="checkin-card">
                        <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Quick Check-in/Out</h3>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-search text-lg" style="color: var(--text-muted);"></i>
                            </div>
                            <input type="text"
                                   id="member-search"
                                   placeholder="Search by member name, email, or ID..."
                                   class="search-input pl-12">
                        </div>
                        <div id="search-results" class="mt-4 space-y-2 hidden">
                            <!-- Search results will be populated here -->
                        </div>
                    </div>

                    <!-- Current Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Currently Checked In -->
                        <div class="checkin-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Currently Checked In</h3>
                                <span class="status-badge status-checked-in">47 Active</span>
                            </div>

                            <div class="space-y-3 max-h-96 overflow-y-auto">
                                <div class="flex items-center justify-between p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full bg-gym-primary flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">SJ</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium" style="color: var(--text-primary);">Sarah Johnson</div>
                                            <div class="text-xs" style="color: var(--text-muted);">Checked in: 2:30 PM</div>
                                        </div>
                                    </div>
                                    <button class="btn-checkout">
                                        <i class="fas fa-sign-out-alt mr-1"></i>
                                        Check Out
                                    </button>
                                </div>

                                <div class="flex items-center justify-between p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full bg-gym-secondary flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">MR</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium" style="color: var(--text-primary);">Mike Rodriguez</div>
                                            <div class="text-xs" style="color: var(--text-muted);">Checked in: 1:15 PM</div>
                                        </div>
                                    </div>
                                    <button class="btn-checkout">
                                        <i class="fas fa-sign-out-alt mr-1"></i>
                                        Check Out
                                    </button>
                                </div>

                                <div class="flex items-center justify-between p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full bg-gym-accent flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">EC</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium" style="color: var(--text-primary);">Emily Chen</div>
                                            <div class="text-xs" style="color: var(--text-muted);">Checked in: 12:45 PM</div>
                                        </div>
                                    </div>
                                    <button class="btn-checkout">
                                        <i class="fas fa-sign-out-alt mr-1"></i>
                                        Check Out
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Check-outs -->
                        <div class="checkin-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Recent Check-outs</h3>
                                <span class="status-badge status-checked-out">Today</span>
                            </div>

                            <div class="space-y-3 max-h-96 overflow-y-auto">
                                <div class="flex items-center justify-between p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">JD</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium" style="color: var(--text-primary);">John Davis</div>
                                            <div class="text-xs" style="color: var(--text-muted);">Checked out: 2:00 PM</div>
                                        </div>
                                    </div>
                                    <button class="btn-checkin">
                                        <i class="fas fa-sign-in-alt mr-1"></i>
                                        Check In
                                    </button>
                                </div>

                                <div class="flex items-center justify-between p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">LW</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium" style="color: var(--text-primary);">Lisa Wilson</div>
                                            <div class="text-xs" style="color: var(--text-muted);">Checked out: 1:30 PM</div>
                                        </div>
                                    </div>
                                    <button class="btn-checkin">
                                        <i class="fas fa-sign-in-alt mr-1"></i>
                                        Check In
                                    </button>
                                </div>

                                <div class="flex items-center justify-between p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center">
                                            <span class="text-white font-medium text-sm">TS</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium" style="color: var(--text-primary);">Tom Smith</div>
                                            <div class="text-xs" style="color: var(--text-muted);">Checked out: 12:15 PM</div>
                                        </div>
                                    </div>
                                    <button class="btn-checkin">
                                        <i class="fas fa-sign-in-alt mr-1"></i>
                                        Check In
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Theme Manager
        class ThemeManager {
            constructor() {
                this.currentTheme = localStorage.getItem('gym-theme') || 'dark';
                this.setTheme(this.currentTheme);
                this.initializeToggle();
            }

            setTheme(theme) {
                this.currentTheme = theme;
                document.body.setAttribute('data-theme', theme);
                localStorage.setItem('gym-theme', theme);
                this.updateThemeToggleUI();
            }

            toggleTheme() {
                const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            }

            updateThemeToggleUI() {
                const toggleButton = document.getElementById('theme-toggle');
                const themeIcon = document.getElementById('theme-icon');

                if (toggleButton && themeIcon) {
                    if (this.currentTheme === 'light') {
                        toggleButton.classList.add('active');
                        toggleButton.title = 'Switch to dark theme';
                        toggleButton.setAttribute('aria-label', 'Switch to dark theme');
                        themeIcon.className = 'fas fa-sun header-toggle-icon';
                    } else {
                        toggleButton.classList.remove('active');
                        toggleButton.title = 'Switch to light theme';
                        toggleButton.setAttribute('aria-label', 'Switch to light theme');
                        themeIcon.className = 'fas fa-moon header-toggle-icon';
                    }
                }
            }

            initializeToggle() {
                const themeToggle = document.getElementById('theme-toggle');
                if (themeToggle) {
                    themeToggle.addEventListener('click', () => this.toggleTheme());
                }
            }
        }

        // Fullscreen Manager
        class FullscreenManager {
            constructor() {
                this.isFullscreen = false;
                this.initializeToggle();
                this.initializeEventListeners();
            }

            initializeToggle() {
                const fullscreenToggle = document.getElementById('fullscreen-toggle');
                if (fullscreenToggle) {
                    fullscreenToggle.addEventListener('click', () => this.toggleFullscreen());
                }
            }

            initializeEventListeners() {
                document.addEventListener('fullscreenchange', () => this.updateUI());
                document.addEventListener('webkitfullscreenchange', () => this.updateUI());
                document.addEventListener('mozfullscreenchange', () => this.updateUI());
                document.addEventListener('MSFullscreenChange', () => this.updateUI());
            }

            async toggleFullscreen() {
                try {
                    if (this.isCurrentlyFullscreen()) {
                        await this.exitFullscreen();
                    } else {
                        await this.enterFullscreen();
                    }
                } catch (error) {
                    console.error('Fullscreen toggle failed:', error);
                }
            }

            async enterFullscreen() {
                const element = document.documentElement;
                if (element.requestFullscreen) {
                    await element.requestFullscreen();
                } else if (element.webkitRequestFullscreen) {
                    await element.webkitRequestFullscreen();
                } else if (element.mozRequestFullScreen) {
                    await element.mozRequestFullScreen();
                } else if (element.msRequestFullscreen) {
                    await element.msRequestFullscreen();
                }
            }

            async exitFullscreen() {
                if (document.exitFullscreen) {
                    await document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    await document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    await document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    await document.msExitFullscreen();
                }
            }

            isCurrentlyFullscreen() {
                return !!(document.fullscreenElement ||
                         document.webkitFullscreenElement ||
                         document.mozFullScreenElement ||
                         document.msFullscreenElement);
            }

            updateUI() {
                const isFullscreen = this.isCurrentlyFullscreen();
                const button = document.getElementById('fullscreen-toggle');
                const fullscreenIcon = document.getElementById('fullscreen-icon');

                if (button && fullscreenIcon) {
                    if (isFullscreen) {
                        button.classList.add('active');
                        button.title = 'Exit fullscreen';
                        button.setAttribute('aria-label', 'Exit fullscreen mode');
                        fullscreenIcon.className = 'fas fa-compress header-toggle-icon';
                    } else {
                        button.classList.remove('active');
                        button.title = 'Enter fullscreen';
                        button.setAttribute('aria-label', 'Enter fullscreen mode');
                        fullscreenIcon.className = 'fas fa-expand header-toggle-icon';
                    }
                }

                this.isFullscreen = isFullscreen;
            }
        }

        // Enhanced Header Manager
        class HeaderManager {
            constructor() {
                this.initializeSearch();
                this.initializeMobileSearch();
                this.initializeKeyboardShortcuts();
            }

            initializeSearch() {
                const globalSearch = document.getElementById('global-search');
                const mobileSearch = document.getElementById('mobile-search');

                if (globalSearch) {
                    globalSearch.addEventListener('input', this.handleSearch.bind(this));
                }

                if (mobileSearch) {
                    mobileSearch.addEventListener('input', this.handleSearch.bind(this));
                }
            }

            handleSearch(event) {
                const query = event.target.value.trim();
                if (query.length > 2) {
                    console.log('Searching for:', query);
                }
            }

            initializeMobileSearch() {
                const mobileSearchToggle = document.getElementById('mobile-search-toggle');
                const mobileSearchOverlay = document.getElementById('mobile-search-overlay');
                const mobileSearchClose = document.getElementById('mobile-search-close');
                const mobileSearchInput = document.getElementById('mobile-search');

                if (mobileSearchToggle && mobileSearchOverlay) {
                    mobileSearchToggle.addEventListener('click', () => {
                        mobileSearchOverlay.classList.toggle('hidden');
                        if (!mobileSearchOverlay.classList.contains('hidden')) {
                            setTimeout(() => mobileSearchInput?.focus(), 100);
                        }
                    });
                }

                if (mobileSearchClose) {
                    mobileSearchClose.addEventListener('click', () => {
                        mobileSearchOverlay.classList.add('hidden');
                    });
                }

                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && !mobileSearchOverlay?.classList.contains('hidden')) {
                        mobileSearchOverlay.classList.add('hidden');
                    }
                });
            }

            initializeKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                        e.preventDefault();
                        const searchInput = document.getElementById('global-search') || document.getElementById('mobile-search');
                        if (searchInput) {
                            if (window.innerWidth < 768) {
                                document.getElementById('mobile-search-toggle')?.click();
                            } else {
                                searchInput.focus();
                            }
                        }
                    }
                });
            }
        }

        // Check-in Manager
        class CheckinManager {
            constructor() {
                this.initializeMemberSearch();
                this.initializeCheckInOut();
            }

            initializeMemberSearch() {
                const memberSearch = document.getElementById('member-search');
                const searchResults = document.getElementById('search-results');

                if (memberSearch) {
                    memberSearch.addEventListener('input', (e) => {
                        const query = e.target.value.trim();
                        if (query.length > 2) {
                            this.searchMembers(query);
                        } else {
                            searchResults.classList.add('hidden');
                        }
                    });
                }
            }

            searchMembers(query) {
                // Mock search results
                const mockMembers = [
                    { id: 1, name: 'Sarah Johnson', email: '<EMAIL>', status: 'checked-out' },
                    { id: 2, name: 'Mike Rodriguez', email: '<EMAIL>', status: 'checked-in' },
                    { id: 3, name: 'Emily Chen', email: '<EMAIL>', status: 'checked-out' },
                    { id: 4, name: 'John Davis', email: '<EMAIL>', status: 'checked-out' },
                    { id: 5, name: 'Lisa Wilson', email: '<EMAIL>', status: 'checked-in' }
                ];

                const filteredMembers = mockMembers.filter(member =>
                    member.name.toLowerCase().includes(query.toLowerCase()) ||
                    member.email.toLowerCase().includes(query.toLowerCase())
                );

                this.displaySearchResults(filteredMembers);
            }

            displaySearchResults(members) {
                const searchResults = document.getElementById('search-results');

                if (members.length === 0) {
                    searchResults.innerHTML = '<p class="text-center py-4" style="color: var(--text-muted);">No members found</p>';
                } else {
                    searchResults.innerHTML = members.map(member => `
                        <div class="flex items-center justify-between p-3 rounded-lg" style="background-color: var(--hover-bg);">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 rounded-full bg-gym-primary flex items-center justify-center">
                                    <span class="text-white font-medium text-sm">${member.name.split(' ').map(n => n[0]).join('')}</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium" style="color: var(--text-primary);">${member.name}</div>
                                    <div class="text-xs" style="color: var(--text-muted);">${member.email}</div>
                                </div>
                            </div>
                            <button class="${member.status === 'checked-in' ? 'btn-checkout' : 'btn-checkin'}"
                                    onclick="window.checkinManager.toggleMemberStatus(${member.id}, '${member.status}')">
                                <i class="fas fa-sign-${member.status === 'checked-in' ? 'out' : 'in'}-alt mr-1"></i>
                                ${member.status === 'checked-in' ? 'Check Out' : 'Check In'}
                            </button>
                        </div>
                    `).join('');
                }

                searchResults.classList.remove('hidden');
            }

            initializeCheckInOut() {
                // Add event listeners for check-in/out buttons
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('btn-checkin') || e.target.closest('.btn-checkin')) {
                        const button = e.target.classList.contains('btn-checkin') ? e.target : e.target.closest('.btn-checkin');
                        this.handleCheckIn(button);
                    } else if (e.target.classList.contains('btn-checkout') || e.target.closest('.btn-checkout')) {
                        const button = e.target.classList.contains('btn-checkout') ? e.target : e.target.closest('.btn-checkout');
                        this.handleCheckOut(button);
                    }
                });
            }

            handleCheckIn(button) {
                console.log('Member checked in');
                // Add check-in logic here
                this.showNotification('Member checked in successfully', 'success');
            }

            handleCheckOut(button) {
                console.log('Member checked out');
                // Add check-out logic here
                this.showNotification('Member checked out successfully', 'success');
            }

            toggleMemberStatus(memberId, currentStatus) {
                if (currentStatus === 'checked-in') {
                    this.handleCheckOut(null);
                } else {
                    this.handleCheckIn(null);
                }
            }

            showNotification(message, type) {
                // Simple notification system
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
                    type === 'success' ? 'bg-green-500' : 'bg-red-500'
                }`;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }
        }

        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize managers
            window.themeManager = new ThemeManager();
            window.fullscreenManager = new FullscreenManager();
            window.headerManager = new HeaderManager();
            window.checkinManager = new CheckinManager();

            // Mobile menu toggle
            const openSidebar = document.getElementById('open-sidebar');
            const closeSidebar = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');

            function initializeSidebar() {
                if (window.innerWidth < 1024) {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');
                    overlay.classList.add('hidden');
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';
                } else {
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    overlay.classList.add('hidden');
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';
                }
            }

            function openSidebarMenu() {
                sidebar.classList.remove('-translate-x-full');
                sidebar.classList.add('translate-x-0');
                overlay.classList.remove('hidden');
                document.body.classList.add('sidebar-open');
                document.body.style.overflow = 'hidden';
            }

            function closeSidebarMenu() {
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
                overlay.classList.add('hidden');
                document.body.classList.remove('sidebar-open');
                document.body.style.overflow = '';
            }

            if (openSidebar) {
                openSidebar.addEventListener('click', openSidebarMenu);
            }

            if (closeSidebar) {
                closeSidebar.addEventListener('click', closeSidebarMenu);
            }

            if (overlay) {
                overlay.addEventListener('click', closeSidebarMenu);
            }

            window.addEventListener('resize', initializeSidebar);
            initializeSidebar();
        });
    </script>
</body>
</html>
