<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitTrack - Gym Member Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // ===================================================================================
        // KONFIGURASI UTAMA TAILWIND
        // Semua styling (warna, font, dll) kini terpusat di sini.
        // ===================================================================================
        tailwind.config = {
            darkMode: 'class', // Mengaktifkan mode 'dark' berbasis class pada elemen <html>
            theme: {
                extend: {
                    colors: {
                        // WARNA UNTUK LIGHT MODE (DEFAULT)
                        'primary': '#ffffff',
                        'secondary': '#f8fafc',
                        'tertiary': '#e2e8f0',
                        'card-bg': '#ffffff',
                        'input-bg': '#f1f5f9',
                        'hover-bg': '#f1f5f9',
                        
                        'text-primary': '#1e293b',
                        'text-secondary': '#475569',
                        'text-muted': '#64748b',

                        'border-primary': '#e2e8f0',
                        'border-secondary': '#cbd5e1',

                        // WARNA UNTUK DARK MODE (GUNAKAN DENGAN PREFIX `dark:`)
                        'dark': {
                            'primary': '#111827',
                            'secondary': '#1f2937',
                            'tertiary': '#374151',
                            'card-bg': '#1f2937', // Menggunakan warna sedikit lebih terang untuk card
                            'input-bg': '#374151',
                            'hover-bg': 'rgba(55, 65, 81, 0.5)',

                            'text-primary': '#f9fafb',
                            'text-secondary': '#d1d5db',
                            'text-muted': '#9ca3af',

                            'border-primary': '#374151',
                            'border-secondary': '#4b5563',
                        },
                        
                        // WARNA AKSEN (TETAP SAMA UNTUK KEDUA TEMA)
                        'gym-primary': '#33AADA',
                        'gym-secondary': '#F59E0B',
                        'gym-accent': '#3B82F6',
                        'gym-blue': '#337ADE',
                    },
                    backgroundImage: {
                        'gradient-primary': 'linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%)',
                    }
                }
            }
        }
    </script>
    <style>
        /* CSS Kustom yang benar-benar diperlukan saja */
        .gradient-text {
            background: var(--gradient-primary, linear-gradient(135deg, #8554F5, #3FE0D0));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        .sidebar-scroll::-webkit-scrollbar { width: 6px; }
        .sidebar-scroll::-webkit-scrollbar-track { background: transparent; }
        .sidebar-scroll::-webkit-scrollbar-thumb { background: transparent; border-radius: 10px; }
        .sidebar-scroll:hover::-webkit-scrollbar-thumb { background: #555; }

        .section {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="font-sans bg-secondary text-text-primary dark:bg-dark-primary dark:text-dark-text-primary transition-colors duration-300">
    
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 hidden lg:hidden"></div>

    <div class="flex h-screen">
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 flex flex-col transform -translate-x-full lg:translate-x-0 lg:relative 
                                bg-primary border-r border-border-primary 
                                dark:bg-dark-secondary dark:border-dark-border-primary 
                                transition-transform duration-300 ease-in-out">
            <div class="flex items-center justify-between h-16 px-4 flex-shrink-0 border-b border-border-primary dark:border-dark-border-primary">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gym-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-sm"></i>
                    </div>
                    <span class="gradient-text text-xl font-bold">FitTrack</span>
                </div>
                <button id="close-sidebar" class="lg:hidden text-text-muted hover:text-text-primary">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>

            <div class="flex-1 overflow-y-auto sidebar-scroll">
                <nav class="mt-4 px-3 pb-4">
                    <div class="space-y-1">
                        <a href="#dashboard" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i class="fas fa-tachometer-alt w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                            Dashboard
                        </a>
                        <a href="#members" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i class="fas fa-users w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                            Members
                        </a>
                        <a href="#registration" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i class="fas fa-user-plus w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                            Registration
                        </a>
                        <a href="#checkin" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i class="fas fa-sign-in-alt w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                            Check-in/Out
                        </a>
                        <a href="#memberships" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i class="fas fa-credit-card w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                            Memberships
                        </a>
                         <a href="#billing" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                            <i class="fas fa-receipt w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                            Billing
                        </a>
                        <a href="#schedule" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                           <i class="fas fa-calendar-alt w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                           Class Schedule
                       </a>
                       <a href="#statistics" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                           <i class="fas fa-chart-bar w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                           Statistics
                       </a>
                    </div>
                    <div class="mt-6 pt-6 border-t border-border-primary dark:border-dark-border-primary">
                        <div class="space-y-1">
                             <a href="#settings" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                                 <i class="fas fa-cog w-5 h-5 mr-3 text-text-muted group-hover:text-text-primary transition-colors"></i>
                                 Settings
                             </a>
                             <a href="#logout" class="nav-link group flex items-center px-3 py-2 text-sm font-medium rounded-lg text-red-500 hover:bg-red-500/10">
                                 <i class="fas fa-sign-out-alt w-5 h-5 mr-3 text-red-500"></i>
                                 Logout
                             </a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="flex-shrink-0 bg-primary dark:bg-dark-primary border-b border-border-primary dark:border-dark-border-primary">
                <div class="flex items-center justify-between h-16 px-6">
                    <div class="flex items-center space-x-4">
                        <button id="open-sidebar" class="lg:hidden text-text-muted hover:text-text-primary">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div>
                            <h1 id="header-title" class="text-xl font-bold text-text-primary dark:text-dark-text-primary">Dashboard</h1>
                            <p class="text-text-muted dark:text-dark-text-muted text-sm">Welcome back, Admin</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-3">
                             <span class="hidden md:block text-sm font-medium text-text-secondary dark:text-dark-text-secondary">Theme</span>
                             <button id="theme-toggle" class="relative w-14 h-7 bg-tertiary dark:bg-dark-tertiary rounded-full flex items-center p-1 transition-colors duration-300">
                                 <div class="absolute inset-0 flex items-center justify-between px-2">
                                     <i class="fas fa-moon text-yellow-300 moon-icon"></i>
                                     <i class="fas fa-sun text-yellow-500 sun-icon"></i>
                                 </div>
                                 <div id="theme-knob" class="w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-300"></div>
                             </button>
                        </div>

                        <div class="relative">
                            <button id="notification-btn" class="relative p-2 text-text-muted hover:text-text-primary dark:hover:text-dark-text-primary rounded-full hover:bg-hover-bg dark:hover:bg-dark-hover-bg transition-colors">
                                <i class="fas fa-bell text-xl"></i>
                                <span id="notification-badge" class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
                            </button>

                            <div id="notification-dropdown" 
                                 class="absolute top-full right-0 mt-2 w-80 rounded-xl shadow-2xl bg-primary border border-border-primary dark:bg-dark-secondary dark:border-dark-border-primary hidden
                                        origin-top-right transition-all duration-200 ease-in-out opacity-0 scale-95"
                                 role="dialog" aria-modal="true" aria-labelledby="notification-heading">
                                
                                <div class="p-4 border-b border-border-primary dark:border-dark-border-primary">
                                    <div class="flex items-center justify-between">
                                        <h3 id="notification-heading" class="text-lg font-semibold text-text-primary dark:text-dark-text-primary">Notifications</h3>
                                        <button id="mark-all-read" class="text-gym-primary hover:underline text-sm font-medium">Mark all read</button>
                                    </div>
                                </div>

                                <div id="notifications-list" class="max-h-96 overflow-y-auto">
                                    <div id="empty-notification-state" class="hidden text-center py-12 px-4">
                                         <i class="fas fa-check-circle text-4xl text-gray-300 dark:text-gray-600 mx-auto"></i>
                                         <h3 class="mt-2 font-semibold text-text-primary dark:text-dark-text-primary">All caught up!</h3>
                                         <p class="text-sm text-text-muted dark:text-dark-text-muted">You have no new notifications.</p>
                                    </div>
                                    </div>

                                <div class="p-2 border-t border-border-primary dark:border-dark-border-primary">
                                    <button class="w-full text-center text-gym-primary hover:bg-gym-primary/10 py-2 rounded-lg text-sm font-medium transition-colors">
                                        View all notifications
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div class="w-9 h-9 rounded-full bg-cover bg-center" style="background-image: url('https://i.pravatar.cc/100?u=admin')"></div>
                             <span class="hidden sm:block text-sm font-medium text-text-primary dark:text-dark-text-primary">Admin User</span>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-y-auto p-6">
                <div id="dashboard-section" class="section">
                    <h2 class="text-2xl font-bold mb-4">Dashboard Content</h2>
                    </div>
                <div id="members-section" class="section hidden">
                     <h2 class="text-2xl font-bold mb-4">Members Content</h2>
                     </div>
                <div id="registration-section" class="section hidden">...</div>
                 <div id="checkin-section" class="section hidden">...</div>
                 <div id="memberships-section" class="section hidden">...</div>
                 <div id="billing-section" class="section hidden">...</div>
                 <div id="schedule-section" class="section hidden">...</div>
                 <div id="statistics-section" class="section hidden">...</div>
                 <div id="settings-section" class="section hidden">...</div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- THEME MANAGEMENT ---
            const themeToggleBtn = document.getElementById('theme-toggle');
            const themeKnob = document.getElementById('theme-knob');
            
            function setTheme(theme) {
                localStorage.setItem('theme', theme);
                if (theme === 'dark') {
                    document.documentElement.classList.add('dark');
                    themeKnob.style.transform = 'translateX(0px)';
                } else {
                    document.documentElement.classList.remove('dark');
                    themeKnob.style.transform = 'translateX(28px)';
                }
            }

            themeToggleBtn.addEventListener('click', () => {
                const currentTheme = localStorage.getItem('theme') || 'dark';
                setTheme(currentTheme === 'dark' ? 'light' : 'dark');
            });
            
            // Inisialisasi tema saat halaman dimuat
            setTheme(localStorage.getItem('theme') || 'dark');

            // --- MOBILE SIDEBAR ---
            const openSidebarBtn = document.getElementById('open-sidebar');
            const closeSidebarBtn = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');

            function openSidebar() {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
            }
            function closeSidebar() {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
            }

            openSidebarBtn.addEventListener('click', openSidebar);
            closeSidebarBtn.addEventListener('click', closeSidebar);
            overlay.addEventListener('click', closeSidebar);


            // --- PAGE NAVIGATION (SPA-like) ---
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.section');
            const headerTitle = document.getElementById('header-title');

            function setActiveLink(link) {
                navLinks.forEach(l => l.classList.remove('active', 'bg-hover-bg', 'dark:bg-dark-hover-bg', 'text-text-primary', 'dark:text-dark-text-primary'));
                link.classList.add('active', 'bg-hover-bg', 'dark:bg-dark-hover-bg', 'text-text-primary', 'dark:text-dark-text-primary');
                
                // Style untuk link active
                navLinks.forEach(l => {
                    l.style.borderLeft = l.classList.contains('active') ? '3px solid #33AADA' : '3px solid transparent';
                    l.style.color = l.classList.contains('active') ? 'var(--text-primary)' : 'var(--text-secondary)';
                });
            }

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId + '-section');
                    
                    sections.forEach(s => s.classList.add('hidden'));
                    if(targetSection) {
                        targetSection.classList.remove('hidden');
                    }
                    
                    headerTitle.textContent = this.textContent.trim();
                    setActiveLink(this);
                    
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });
            
            // Set default active link
            const initialActiveLink = document.querySelector('.nav-link[href="#dashboard"]');
            if (initialActiveLink) {
                initialActiveLink.click();
            }


            // --- NOTIFICATION DROPDOWN ---
            const notifBtn = document.getElementById('notification-btn');
            const notifDropdown = document.getElementById('notification-dropdown');

            notifBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const isHidden = notifDropdown.classList.contains('hidden');
                if (isHidden) {
                    notifDropdown.classList.remove('hidden');
                    setTimeout(() => {
                        notifDropdown.classList.remove('opacity-0', 'scale-95');
                    }, 10);
                } else {
                    notifDropdown.classList.add('opacity-0', 'scale-95');
                    setTimeout(() => {
                        notifDropdown.classList.add('hidden');
                    }, 200);
                }
            });

            // Close when clicking outside
            document.addEventListener('click', (e) => {
                if (!notifDropdown.contains(e.target) && !notifBtn.contains(e.target)) {
                    notifDropdown.classList.add('opacity-0', 'scale-95');
                    setTimeout(() => {
                        notifDropdown.classList.add('hidden');
                    }, 200);
                }
            });

            // (Logika untuk render notifikasi, mark as read, dll bisa ditambahkan di sini)
        });
    </script>
</body>
</html>